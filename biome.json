{"$schema": "https://biomejs.dev/schemas/2.0.6/schema.json", "files": {"includes": ["**", "!**/node_modules/**", "!**/dist/**", "!**/src/paraglide", "!**/src/data/scripts/itemData/itemData.json", "!**/src/data/scripts/items/items.json", "!**/src/data/items.js"]}, "assist": {"actions": {"source": {"organizeImports": "on"}}}, "formatter": {"enabled": true, "lineWidth": 100, "indentStyle": "tab", "indentWidth": 4, "formatWithErrors": false}, "javascript": {"formatter": {"trailingCommas": "all"}}, "linter": {"enabled": true, "includes": ["**", "!**/dbschema/**"], "rules": {"recommended": true, "style": {"noParameterAssign": "off", "useAsConstAssertion": "error", "useDefaultParameterLast": "error", "useEnumInitializers": "error", "useSelfClosingElements": "error", "useSingleVarDeclarator": "error", "noUnusedTemplateLiteral": "error", "useNumberNamespace": "error", "noInferrableTypes": "error", "noUselessElse": "error"}, "correctness": {"useExhaustiveDependencies": "off", "noUndeclaredVariables": "error"}}}}