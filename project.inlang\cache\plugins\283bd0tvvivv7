var g="messageLintRule.inlang.emptyPattern";var o={en:"Empty pattern"},p={en:"Checks for empty pattern in a language tag. If a message exists in the reference resource but the pattern in a target resource is empty, it is likely that the message has not been translated yet."};var m={id:g,displayName:o,description:p,run:({message:t,settings:i,report:r})=>{let d=i.languageTags.filter(e=>e!==i.sourceLanguageTag);for(let e of d){let l=t.variants.filter(n=>n.languageTag===e)??[];if(l.length===0)return;let a=l.flatMap(({pattern:n})=>n);a.length?a.length===1&&a[0]?.type==="Text"&&a[0]?.value===""&&r({messageId:t.id,languageTag:e,body:{en:`Message with id '${t.id}' has no content for language tag '${e}'.`}}):r({messageId:t.id,languageTag:e,body:{en:`Message with id '${t.id}' has no patterns for language tag '${e}'.`}})}}};var f=m;export{f as default};
