{"name": "albion-profit", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "mongodb:start": "mongod --dbpath ./src/data/mongodb/data/db --port 27018", "build": "paraglide-js compile --project ./project.inlang --outdir ./src\\paraglide && vite build", "preview": "vite preview", "postinstall": "paraglide-js compile --project ./project.inlang --outdir ./src\\paraglide"}, "dependencies": {"@formkit/auto-animate": "^0.8.2", "@icaruk/debounce": "^1.1.0", "@inlang/paraglide-vite": "^1.4.0", "@mantine/charts": "^8.1.2", "@mantine/core": "^8.1.2", "@mantine/hooks": "^8.1.2", "@mantine/notifications": "^8.1.2", "@tabler/icons-react": "^3.34.0", "dame": "^1.2.7", "dayjs": "^1.11.13", "mobx": "^6.13.7", "mobx-react-lite": "^4.1.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-router-dom": "^6.23.0", "recharts": "2.15.3"}, "devDependencies": {"@biomejs/biome": "2.0.6", "@inlang/paraglide-js": "^2.1.0", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.6.0", "postcss": "^8.5.6", "postcss-preset-mantine": "1.17.0", "postcss-simple-vars": "^7.0.1", "vite": "^6.3.5"}, "packageManager": "pnpm@10.12.4"}