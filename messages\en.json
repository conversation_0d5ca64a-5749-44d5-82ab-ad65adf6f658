{"$schema": "https://inlang.com/schema/inlang-message-format", "addComponent": "Add component", "fetchPrices": "Fetch prices", "addGroup": "Add group", "resultValue": "Result value", "cost": "Cost", "earnings": "Earnings", "quantity": "Quantity", "price": "Sell price", "total": "Total", "group": "Group", "result": "Result", "components": "Components", "noTax": "No tax", "sellWithPremium": "{num}% (sell with premium)", "sellWithoutPremium": "{num}% (sell without premium)", "sellOrderWithPremium": "{num}% (sell order with premium)", "sellOrderWithoutPremium": "{num}% (sell order without premium)", "earningsAfterTax": "Earnings after tax", "returnRateTooltip": "When crafting or refining an item, you may be given back a certain amount of materials used to make that item. The amount of returned resources depends on the Resource Return Rate.", "bindQuantityTooltip": "Changing the product quantity will change the components quantities with the same ratio.", "bindQuantity": "Bind quantity", "sellOrderPriceChangeCountBeforeLoss": "Number of price changes before losses", "priceIsLockedTooltip": "This lock will prevent the price from being automatically edited.", "shoppingList": "Shopping list", "removeAll": "Remove all", "emptyShoppingListHint": "To add items click on", "getComponents": "Get components", "deleteThisComponent": "Delete this component", "removeFromShoppingList": "Remove from shopping list", "addToShoppingList": "Add to shopping list", "buyPrice": "Buy order", "sellPrice": "Sell price", "priceModeSwitchTooltip": "Switch between buy order or sell order price", "itemData": "Item data"}